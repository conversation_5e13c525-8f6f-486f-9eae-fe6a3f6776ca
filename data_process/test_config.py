#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件是否能正常加载
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加mmaction2到Python路径
mmaction2_path = os.path.join(current_dir, '..', 'mmaction2')
sys.path.insert(0, mmaction2_path)

def test_config_loading():
    """测试配置文件加载"""
    try:
        from mmengine.config import Config
        
        # 加载配置文件
        config_file = 'bmn_multiclass_tad_config.py'
        print(f"🔍 测试配置文件: {config_file}")
        
        cfg = Config.fromfile(config_file)
        print("✅ 配置文件加载成功!")
        
        # 检查自定义变换是否可用
        from mmaction.registry import TRANSFORMS
        
        if 'LoadLocalizationFeatureWithSubdirs' in TRANSFORMS:
            print("✅ 自定义变换 LoadLocalizationFeatureWithSubdirs 已注册")
        else:
            print("❌ 自定义变换 LoadLocalizationFeatureWithSubdirs 未注册")
            print("已注册的变换:", list(TRANSFORMS.keys())[:10], "...")
        
        # 检查数据管道配置
        train_pipeline = cfg.train_pipeline
        print(f"✅ 训练数据管道包含 {len(train_pipeline)} 个步骤")
        
        for i, step in enumerate(train_pipeline):
            print(f"   步骤 {i+1}: {step['type']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transform_instantiation():
    """测试变换实例化"""
    try:
        from mmaction.registry import TRANSFORMS
        
        # 尝试实例化自定义变换
        transform_cfg = dict(type='LoadLocalizationFeatureWithSubdirs')
        transform = TRANSFORMS.build(transform_cfg)
        print("✅ 自定义变换实例化成功")
        print(f"   变换类型: {type(transform)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 变换实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🧪 MMAction2配置文件测试")
    print("=" * 60)
    
    # 测试配置文件加载
    config_ok = test_config_loading()
    
    print("\n" + "-" * 40)
    
    # 测试变换实例化
    if config_ok:
        transform_ok = test_transform_instantiation()
    else:
        transform_ok = False
    
    print("\n" + "=" * 60)
    if config_ok and transform_ok:
        print("🎉 所有测试通过! 配置文件可以正常使用")
    else:
        print("❌ 测试失败，需要进一步修复")
    print("=" * 60)
