# BMN时间动作定位 - SlowOnly特征提取

本项目基于MMAction2官方文档，使用SlowOnly预训练模型为BMN时间动作定位任务提取特征。

## 项目概述

- **数据**: 三类工人动作视频（OK、NOK电气、NOK外观）
- **模型**: SlowOnly R50 (Kinetics-400预训练)
- **特征**: 2048维，100时间步
- **格式**: CSV文件，符合BMN LoadLocalizationFeature要求
- **目标**: 用于BMN时间动作定位模型训练

## 目录结构

```
data_process/
├── README.md                                    # 本文档
├── run_feature_extraction_pipeline.py          # 完整执行脚本
├── generate_video_list.py                      # 生成视频列表
├── slowonly_r50_feature_extraction_config.py   # SlowOnly特征提取配置
├── extract_slowonly_features.py                # 特征提取脚本
├── postprocess_features.py                     # 特征后处理脚本
├── generate_activitynet_annotations.py         # 生成标注文件
├── bmn_multiclass_tad_config.py               # BMN训练配置
└── validate_features.py                        # 特征验证脚本

data/MultiClassTAD/
├── segmented_videos_ok/                         # OK类别视频
├── segmented_videos_nok_electric/               # NOK电气类别视频
├── segmented_videos_nok_appearance/             # NOK外观类别视频
├── features_slowonly/                           # 提取的CSV特征文件
├── video_list.txt                               # 视频列表文件
├── multiclass_tad_train.json                   # 训练集标注
└── multiclass_tad_val.json                     # 验证集标注
```

## 快速开始

### 1. 运行完整流水线

```bash
cd data_process
python run_feature_extraction_pipeline.py
```

### 2. 分步执行

如果需要分步执行或调试：

```bash
# 步骤1: 生成视频列表
python generate_video_list.py

# 步骤2: 生成标注文件
python generate_activitynet_annotations.py

# 步骤3: 提取特征（需要GPU）
python extract_slowonly_features.py

# 步骤4: 后处理特征
python postprocess_features.py

# 步骤5: 验证特征格式
python validate_features.py
```

### 3. 只验证现有特征

```bash
python run_feature_extraction_pipeline.py --only-validate
```

## 技术规范

### SlowOnly模型配置
- **模型**: ResNet3dSlowOnly (depth=50)
- **预训练**: Kinetics-400数据集
- **输入**: 8帧，224x224分辨率
- **输出**: 2048维特征向量

### 特征提取参数
- **长视频模式**: 启用，支持任意长度视频
- **时间采样**: clip_interval=16, frame_interval=1
- **空间池化**: 平均池化
- **时间池化**: 保持原始时间维度

### BMN兼容格式
- **特征维度**: 2048 (行) × 100 (列)
- **文件格式**: CSV，包含header行
- **时间步数**: 100个固定时间步
- **数值精度**: 6位小数

## 数据统计

根据当前数据集：
- **总视频数**: 132个
- **OK类别**: 124个视频
- **NOK电气**: 1个视频  
- **NOK外观**: 7个视频
- **训练/验证分割**: 80%/20%

## BMN模型训练

特征提取完成后，使用提供的BMN配置进行训练：

```bash
cd ../mmaction2
python tools/train.py ../data_process/bmn_multiclass_tad_config.py
```

## 故障排除

### 1. 特征提取失败
- 检查GPU内存是否足够
- 确认MMAction2环境正确安装
- 验证预训练模型下载是否成功

### 2. 特征格式错误
- 运行 `python validate_features.py` 检查具体错误
- 确认CSV文件格式：2048行×100列
- 检查是否存在NaN或无穷大值

### 3. 内存不足
- 减少batch_size
- 使用更小的clip_interval
- 分批处理视频文件

## 依赖要求

- Python 3.8+
- MMAction2
- PyTorch
- OpenCV
- NumPy
- Pandas
- SciPy

## 参考文档

- [MMAction2官方文档](https://mmaction2.readthedocs.io/en/latest/)
- [BMN论文](https://openaccess.thecvf.com/content_ICCV_2019/html/Lin_BMN_Boundary-Matching_Network_for_Temporal_Action_Proposal_Generation_ICCV_2019_paper.html)
- [SlowOnly论文](https://openaccess.thecvf.com/content_ICCV_2019/html/Feichtenhofer_SlowFast_Networks_for_Video_Recognition_ICCV_2019_paper.html)
