#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成符合MMAction2格式的视频列表文件
用于SlowOnly特征提取
"""

import os
import os.path as osp

def generate_video_list():
    """生成视频列表文件"""

    # 数据根目录 - 使用绝对路径
    data_root = '../data/MultiClassTAD'
    
    # 三个类别目录
    categories = [
        'segmented_videos_ok',
        'segmented_videos_nok_electric', 
        'segmented_videos_nok_appearance'
    ]
    
    # 收集所有视频文件
    video_list = []
    
    for category in categories:
        category_path = osp.join(data_root, category)
        if not osp.exists(category_path):
            print(f"警告: 目录不存在 {category_path}")
            continue
            
        # 获取该类别下的所有mp4文件
        video_files = [f for f in os.listdir(category_path) if f.endswith('.mp4')]
        
        for video_file in video_files:
            # MMAction2格式: 相对路径 类别标签
            # 这里我们使用类别索引作为标签 (ok=0, nok_electric=1, nok_appearance=2)
            if category == 'segmented_videos_ok':
                label = 0
            elif category == 'segmented_videos_nok_electric':
                label = 1
            else:  # nok_appearance
                label = 2
                
            # 视频相对路径
            video_path = osp.join(category, video_file)
            video_list.append(f"{video_path} {label}")
    
    # 写入视频列表文件
    output_file = osp.join(data_root, 'video_list.txt')
    with open(output_file, 'w', encoding='utf-8') as f:
        for line in video_list:
            f.write(line + '\n')
    
    print(f"生成视频列表文件: {output_file}")
    print(f"总共 {len(video_list)} 个视频文件")
    
    # 统计各类别数量
    ok_count = sum(1 for line in video_list if line.endswith(' 0'))
    nok_electric_count = sum(1 for line in video_list if line.endswith(' 1'))
    nok_appearance_count = sum(1 for line in video_list if line.endswith(' 2'))
    
    print(f"  - OK类别: {ok_count} 个视频")
    print(f"  - NOK电气类别: {nok_electric_count} 个视频")
    print(f"  - NOK外观类别: {nok_appearance_count} 个视频")
    
    return output_file

if __name__ == '__main__':
    generate_video_list()
