#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征后处理脚本
将SlowOnly提取的特征转换为BMN所需的CSV格式
确保100个时间步和2048维特征
"""

import os
import numpy as np
import pickle
import scipy.interpolate
from pathlib import Path
import argparse
import torch

def pool_feature_to_100_steps(data, num_proposals=100, num_sample_bins=3, pool_type='mean'):
    """
    将任意长度的特征池化到100个时间步
    
    Args:
        data (np.ndarray): 输入特征，形状为 [T, D] 其中T是时间步数，D是特征维度
        num_proposals (int): 目标时间步数，默认100
        num_sample_bins (int): 每个时间步采样的点数，默认3
        pool_type (str): 池化类型，'mean'或'max'
    
    Returns:
        np.ndarray: 池化后的特征，形状为 [num_proposals, D]
    """
    if len(data) == 1:
        # 如果只有一个时间步，复制到所有时间步
        return np.tile(data, (num_proposals, 1))
    
    # 创建插值函数
    x_range = list(range(len(data)))
    f = scipy.interpolate.interp1d(x_range, data, axis=0, kind='linear')
    
    # 计算采样点
    eps = 1e-4
    start, end = eps, len(data) - 1 - eps
    anchor_size = (end - start) / num_proposals
    
    feature = []
    ptr = start
    
    for _ in range(num_proposals):
        # 在当前时间窗口内采样多个点
        x_new = [ptr + i / num_sample_bins * anchor_size for i in range(num_sample_bins)]
        y_new = f(x_new)
        
        # 池化
        if pool_type == 'mean':
            y_new = np.mean(y_new, axis=0)
        elif pool_type == 'max':
            y_new = np.max(y_new, axis=0)
        else:
            raise NotImplementedError(f'不支持的池化类型: {pool_type}')
        
        feature.append(y_new)
        ptr += anchor_size
    
    feature = np.stack(feature)
    return feature

def process_single_video_feature(pkl_file, output_dir, input_dir=None):
    """
    处理单个视频的特征文件

    Args:
        pkl_file (str): 输入的pkl特征文件路径
        output_dir (str): 输出CSV文件的目录
    """
    try:
        # 加载特征
        with open(pkl_file, 'rb') as f:
            features = pickle.load(f)

        # 如果特征是列表，取第一个元素（通常是特征数组）
        if isinstance(features, list):
            features = features[0]

        # 处理PyTorch张量
        if isinstance(features, torch.Tensor):
            features = features.cpu().numpy()

        # 确保特征是numpy数组
        if not isinstance(features, np.ndarray):
            print(f"警告: {pkl_file} 中的特征不是numpy数组或PyTorch张量，跳过")
            return False
        
        print(f"处理 {pkl_file}: 原始特征形状 {features.shape}")

        # 处理特征维度：期望格式是 (batch/clips, feature_dim, time_steps)
        if len(features.shape) == 3:
            # 形状是 (batch, feature_dim, time_steps)
            batch_size, feature_dim, time_steps = features.shape
            print(f"检测到3D特征: batch={batch_size}, feature_dim={feature_dim}, time_steps={time_steps}")

            # 平均所有batch/clips
            features = features.mean(axis=0)  # 结果: (feature_dim, time_steps)
            print(f"平均后特征形状: {features.shape}")

            # 转置为 (time_steps, feature_dim) 以便池化
            features = features.T  # 结果: (time_steps, feature_dim)

        elif len(features.shape) == 2:
            # 如果是2D，检查哪个维度是特征维度
            if features.shape[0] == 2048:
                # (feature_dim, time_steps) -> (time_steps, feature_dim)
                features = features.T
            # 如果features.shape[1] == 2048，则已经是正确格式

        print(f"池化前特征形状: {features.shape}")

        # 检查特征维度
        if features.shape[1] != 2048:
            print(f"警告: 特征维度是 {features.shape[1]}，期望2048")

        # 池化到100个时间步
        pooled_features = pool_feature_to_100_steps(features, num_proposals=100)
        
        print(f"池化后特征形状: {pooled_features.shape}")
        
        # 转置为 [feature_dim, time_steps] 格式（符合BMN期望）
        pooled_features = pooled_features.T  # [2048, 100]
        
        # 生成CSV文件，保持相对路径结构
        pkl_path = Path(pkl_file)
        input_path = Path(input_dir) if input_dir else pkl_path.parent.parent
        relative_path = pkl_path.relative_to(input_path)

        # 创建输出子目录
        output_subdir = Path(output_dir) / relative_path.parent
        output_subdir.mkdir(parents=True, exist_ok=True)

        # CSV文件路径
        csv_file = output_subdir / f"{pkl_path.stem}.csv"
        
        # 写入CSV文件，包含header
        lines = []
        # Header行：f0, f1, ..., f99 (100个时间步)
        header = ','.join([f't{i}' for i in range(pooled_features.shape[1])])
        lines.append(header)
        
        # 数据行：每行是一个特征维度在所有时间步的值
        for i in range(pooled_features.shape[0]):
            line = ','.join([f'{x:.6f}' for x in pooled_features[i]])
            lines.append(line)
        
        # 写入文件
        with open(csv_file, 'w') as f:
            f.write('\n'.join(lines))
        
        print(f"保存CSV文件: {csv_file}")
        return True
        
    except Exception as e:
        print(f"处理 {pkl_file} 时出错: {e}")
        return False

def postprocess_all_features(input_dir, output_dir):
    """
    后处理所有特征文件
    
    Args:
        input_dir (str): 输入pkl文件目录
        output_dir (str): 输出CSV文件目录
    """
    input_path = Path(input_dir)
    output_path = Path(output_dir)
    
    # 创建输出目录
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 递归查找所有pkl文件
    pkl_files = list(input_path.rglob('*.pkl'))

    if not pkl_files:
        print(f"在 {input_dir} 中没有找到pkl文件")
        return
    
    print(f"找到 {len(pkl_files)} 个特征文件")
    
    success_count = 0
    for pkl_file in pkl_files:
        if process_single_video_feature(str(pkl_file), str(output_path), str(input_path)):
            success_count += 1
    
    print(f"\n处理完成: {success_count}/{len(pkl_files)} 个文件成功")

def main():
    parser = argparse.ArgumentParser(description='后处理SlowOnly特征为BMN格式')
    parser.add_argument('--input-dir', default='../data/MultiClassTAD/features_slowonly_raw',
                       help='输入pkl特征文件目录')
    parser.add_argument('--output-dir', default='../data/MultiClassTAD/features_slowonly',
                       help='输出CSV特征文件目录')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_dir):
        print(f"输入目录不存在: {args.input_dir}")
        print("请先运行特征提取脚本")
        return
    
    print("开始后处理SlowOnly特征...")
    postprocess_all_features(args.input_dir, args.output_dir)
    print("特征后处理完成!")

if __name__ == '__main__':
    main()
