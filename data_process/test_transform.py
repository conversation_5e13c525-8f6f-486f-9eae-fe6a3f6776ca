#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试自定义变换的文件名匹配功能
"""

import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# 添加mmaction2到Python路径
mmaction2_path = os.path.join(current_dir, '..', 'mmaction2')
sys.path.insert(0, mmaction2_path)

def test_file_matching():
    """测试文件名匹配功能"""
    try:
        # 导入自定义变换
        from custom_transforms import LoadLocalizationFeatureWithSubdirs
        
        # 创建变换实例
        transform = LoadLocalizationFeatureWithSubdirs()
        
        # 测试文件查找
        base_path = '../data/MultiClassTAD/features_slowonly'
        test_cases = [
            '20250727T075940Z_20250727T080440Z_decrypted-00.00.18.469-00.00.21.687-seg01_roi',  # 带_roi后缀
            '20250728T082248Z_20250728T082748Z_decrypted_roi-00.01.16.815-************-seg10',   # 不带_roi后缀
        ]
        
        print(f"🔍 测试文件查找:")
        print(f"   基础路径: {base_path}")

        all_success = True

        for i, test_video_name in enumerate(test_cases, 1):
            print(f"\n   测试案例 {i}: {test_video_name}")

            # 查找文件
            found_file = transform._find_feature_file(base_path, test_video_name)

            if found_file:
                print(f"   ✅ 找到特征文件: {found_file}")

                # 检查文件是否真的存在
                if os.path.exists(found_file):
                    print(f"   ✅ 文件确实存在")

                    # 检查文件大小
                    file_size = os.path.getsize(found_file)
                    print(f"      文件大小: {file_size} 字节")
                else:
                    print(f"   ❌ 文件路径返回但文件不存在")
                    all_success = False
            else:
                print(f"   ❌ 未找到特征文件")
                all_success = False

        return all_success
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_transform_pipeline():
    """测试完整的变换管道"""
    try:
        from custom_transforms import LoadLocalizationFeatureWithSubdirs
        
        # 创建变换实例
        transform = LoadLocalizationFeatureWithSubdirs()
        
        # 模拟数据输入 - 测试第二个案例（不带_roi后缀的）
        results = {
            'feature_path': '../data/MultiClassTAD/features_slowonly',
            'video_name': '20250728T082248Z_20250728T082748Z_decrypted_roi-00.01.16.815-************-seg10'
        }
        
        print(f"\n🧪 测试变换管道:")
        print(f"   输入: {results}")
        
        # 执行变换
        output = transform.transform(results)
        
        print(f"✅ 变换成功执行")
        print(f"   输出键: {list(output.keys())}")
        
        if 'raw_feature' in output:
            feature_shape = output['raw_feature'].shape
            print(f"   特征形状: {feature_shape}")
            print(f"   特征类型: {type(output['raw_feature'])}")
            
            return True
        else:
            print(f"❌ 输出中缺少 'raw_feature' 键")
            return False
            
    except Exception as e:
        print(f"❌ 变换管道测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("🧪 自定义变换测试")
    print("=" * 60)
    
    # 测试文件匹配
    file_match_ok = test_file_matching()
    
    # 测试变换管道
    if file_match_ok:
        transform_ok = test_transform_pipeline()
    else:
        transform_ok = False
    
    print("\n" + "=" * 60)
    if file_match_ok and transform_ok:
        print("🎉 所有测试通过! 自定义变换工作正常")
    else:
        print("❌ 测试失败，需要进一步修复")
    print("=" * 60)
