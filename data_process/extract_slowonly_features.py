#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用MMAction2官方工具提取SlowOnly特征
用于BMN时间动作定位任务
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def download_slowonly_checkpoint():
    """下载SlowOnly R50 Kinetics-400预训练模型"""
    checkpoint_url = ('https://download.openmmlab.com/mmaction/v1.0/recognition/slowonly/'
                     'slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_kinetics400-'
                     'rgb/slowonly_imagenet-pretrained-r50_8xb16-4x16x1-steplr-150e_'
                     'kinetics400-rgb_20220901-e7b65fad.pth')
    
    checkpoint_dir = Path('../work_dirs/checkpoints')
    checkpoint_dir.mkdir(parents=True, exist_ok=True)
    
    checkpoint_path = checkpoint_dir / 'slowonly_r50_kinetics400.pth'
    
    if not checkpoint_path.exists():
        print(f"下载SlowOnly R50预训练模型...")
        cmd = f"wget -O {checkpoint_path} {checkpoint_url}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print(f"下载失败: {result.stderr}")
            return None
        print(f"模型下载完成: {checkpoint_path}")
    else:
        print(f"使用已存在的模型: {checkpoint_path}")
    
    return str(checkpoint_path)

def extract_features():
    """提取SlowOnly特征"""
    
    # 配置文件和输出路径
    config_file = 'slowonly_r50_feature_extraction_config.py'
    video_list = '../data/MultiClassTAD/video_list.txt'
    video_root = '../data/MultiClassTAD'
    output_prefix = '../data/MultiClassTAD/features_slowonly_raw'
    
    # 确保输出目录存在
    Path(output_prefix).mkdir(parents=True, exist_ok=True)
    
    # 下载预训练模型
    checkpoint_path = download_slowonly_checkpoint()
    if checkpoint_path is None:
        print("无法下载预训练模型，退出")
        return False
    
    # 构建特征提取命令
    # 使用长视频模式以获得更密集的特征
    cmd = [
        'python', '../mmaction2/tools/misc/clip_feature_extraction.py',
        config_file,
        checkpoint_path,
        output_prefix,
        '--video-list', video_list,
        '--video-root', video_root,
        '--long-video-mode',
        '--clip-interval', '16',  # 每16帧提取一个clip
        '--frame-interval', '1',  # 帧间隔为1，获得更密集的采样
        '--spatial-type', 'avg',  # 空间维度平均池化
        '--temporal-type', 'keep'  # 保持时间维度，不进行时间池化
    ]
    
    print("开始提取SlowOnly特征...")
    print(f"命令: {' '.join(cmd)}")
    
    # 执行特征提取
    try:
        result = subprocess.run(cmd, cwd='.', capture_output=True, text=True)
        
        if result.returncode == 0:
            print("特征提取完成!")
            print("输出:", result.stdout)
        else:
            print("特征提取失败!")
            print("错误:", result.stderr)
            return False
            
    except Exception as e:
        print(f"执行特征提取时出错: {e}")
        return False
    
    return True

def main():
    parser = argparse.ArgumentParser(description='提取SlowOnly特征用于BMN')
    parser.add_argument('--config', default='slowonly_r50_feature_extraction_config.py',
                       help='特征提取配置文件')
    
    args = parser.parse_args()
    
    # 检查必要文件是否存在
    if not os.path.exists(args.config):
        print(f"配置文件不存在: {args.config}")
        return
    
    if not os.path.exists('../data/MultiClassTAD/video_list.txt'):
        print("视频列表文件不存在，请先运行 generate_video_list.py")
        return
    
    # 提取特征
    success = extract_features()
    
    if success:
        print("\n特征提取完成!")
        print("下一步请运行特征后处理脚本将特征转换为BMN所需的CSV格式")
    else:
        print("\n特征提取失败，请检查错误信息")

if __name__ == '__main__':
    main()
