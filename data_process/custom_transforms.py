#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义数据变换类
用于处理子目录结构的特征文件加载
"""

import os
import numpy as np
from pathlib import Path
from mmcv.transforms import BaseTransform
from mmaction.registry import TRANSFORMS


@TRANSFORMS.register_module()
class LoadLocalizationFeatureWithSubdirs(BaseTransform):
    """
    加载时间动作定位特征文件，支持子目录结构
    
    这个类扩展了原始的LoadLocalizationFeature，增加了对子目录的支持。
    它会在指定的特征目录及其子目录中搜索特征文件。
    
    兼容性：
    - 如果特征文件在根目录下，行为与原始LoadLocalizationFeature完全相同
    - 如果特征文件在子目录中，会自动搜索并加载
    
    Args:
        raw_feature_ext (str): 特征文件扩展名。默认: '.csv'
        search_subdirs (bool): 是否搜索子目录。默认: True
    """
    
    def __init__(self, raw_feature_ext='.csv', search_subdirs=True):
        self.raw_feature_ext = raw_feature_ext
        self.search_subdirs = search_subdirs
    
    def _find_feature_file(self, base_path, video_name):
        """
        查找特征文件，支持子目录搜索
        
        Args:
            base_path (str): 基础路径（特征目录）
            video_name (str): 视频名称（不含扩展名）
            
        Returns:
            str: 特征文件的完整路径，如果未找到则返回None
        """
        base_path = Path(base_path)
        feature_filename = video_name + self.raw_feature_ext
        
        # 首先在根目录查找（保持原始兼容性）
        direct_path = base_path / feature_filename
        if direct_path.exists():
            return str(direct_path)
        
        # 如果启用子目录搜索，则在子目录中查找
        if self.search_subdirs:
            # 使用rglob递归搜索所有子目录
            for feature_file in base_path.rglob(feature_filename):
                return str(feature_file)
        
        return None
    
    def transform(self, results):
        """
        执行特征加载变换
        
        Args:
            results (dict): 包含数据信息的字典，需要包含'feature_path'键
            
        Returns:
            dict: 添加了'raw_feature'键的结果字典
        """
        # 获取特征路径信息
        feature_path = results['feature_path']
        
        # 如果feature_path是完整的文件路径，直接使用
        if os.path.isfile(feature_path):
            data_path = feature_path
        else:
            # 否则，从feature_path中提取目录和视频名称
            if os.path.isdir(feature_path):
                # feature_path是目录，需要从results中获取视频名称
                video_name = results.get('video_name', '')
                if not video_name:
                    raise ValueError("当feature_path是目录时，results中必须包含'video_name'")
                
                # 查找特征文件
                data_path = self._find_feature_file(feature_path, video_name)
                if data_path is None:
                    raise FileNotFoundError(
                        f"在 {feature_path} 及其子目录中未找到特征文件: {video_name}{self.raw_feature_ext}"
                    )
            else:
                # feature_path可能是不完整的路径，尝试直接使用
                data_path = feature_path
        
        # 加载特征文件
        try:
            raw_feature = np.loadtxt(
                data_path, dtype=np.float32, delimiter=',', skiprows=1)
        except Exception as e:
            raise RuntimeError(f"加载特征文件失败 {data_path}: {e}")
        
        # 转置特征矩阵：从 (feature_dim, time_steps) 到 (time_steps, feature_dim)
        results['raw_feature'] = np.transpose(raw_feature, (1, 0))
        
        return results
    
    def __repr__(self):
        repr_str = (f'{self.__class__.__name__}('
                   f'raw_feature_ext={self.raw_feature_ext}, '
                   f'search_subdirs={self.search_subdirs})')
        return repr_str


@TRANSFORMS.register_module()
class LoadLocalizationFeatureFlexible(BaseTransform):
    """
    灵活的特征加载器，自动适应不同的路径结构
    
    这个类提供了最大的灵活性，能够处理各种路径结构：
    1. 直接的文件路径
    2. 目录路径 + 视频名称
    3. 带子目录的结构
    
    Args:
        raw_feature_ext (str): 特征文件扩展名。默认: '.csv'
    """
    
    def __init__(self, raw_feature_ext='.csv'):
        self.raw_feature_ext = raw_feature_ext
    
    def transform(self, results):
        """执行特征加载"""
        feature_path = results['feature_path']
        
        # 情况1: feature_path是完整的文件路径
        if os.path.isfile(feature_path):
            data_path = feature_path
        
        # 情况2: feature_path是目录路径
        elif os.path.isdir(feature_path):
            video_name = results.get('video_name', '')
            if not video_name:
                raise ValueError("当feature_path是目录时，results中必须包含'video_name'")
            
            # 构建可能的文件路径
            possible_paths = [
                # 直接在根目录
                os.path.join(feature_path, video_name + self.raw_feature_ext),
                # 在子目录中搜索
            ]
            
            # 添加子目录搜索
            for root, dirs, files in os.walk(feature_path):
                target_file = video_name + self.raw_feature_ext
                if target_file in files:
                    possible_paths.append(os.path.join(root, target_file))
            
            # 找到第一个存在的文件
            data_path = None
            for path in possible_paths:
                if os.path.isfile(path):
                    data_path = path
                    break
            
            if data_path is None:
                raise FileNotFoundError(
                    f"在 {feature_path} 中未找到特征文件: {video_name}{self.raw_feature_ext}")
        
        # 情况3: feature_path可能是不完整的路径
        else:
            data_path = feature_path
        
        # 加载特征
        try:
            raw_feature = np.loadtxt(
                data_path, dtype=np.float32, delimiter=',', skiprows=1)
            results['raw_feature'] = np.transpose(raw_feature, (1, 0))
        except Exception as e:
            raise RuntimeError(f"加载特征文件失败 {data_path}: {e}")
        
        return results
    
    def __repr__(self):
        return f'{self.__class__.__name__}(raw_feature_ext={self.raw_feature_ext})'
