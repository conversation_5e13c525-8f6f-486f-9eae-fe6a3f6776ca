#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证提取特征的格式正确性
检查维度、时间步数和CSV格式是否符合BMN要求
"""

import os
import numpy as np
import pandas as pd
import json
from pathlib import Path
import argparse

def validate_csv_feature(csv_file):
    """
    验证单个CSV特征文件
    
    Args:
        csv_file (str): CSV特征文件路径
    
    Returns:
        dict: 验证结果
    """
    result = {
        'file': csv_file,
        'valid': True,
        'errors': [],
        'warnings': [],
        'shape': None,
        'feature_dim': None,
        'time_steps': None
    }
    
    try:
        # 读取CSV文件
        df = pd.read_csv(csv_file)
        
        # 检查形状
        shape = df.shape
        result['shape'] = shape
        result['feature_dim'] = shape[0]  # 行数是特征维度
        result['time_steps'] = shape[1]   # 列数是时间步数
        
        # 检查时间步数是否为100
        if shape[1] != 100:
            result['valid'] = False
            result['errors'].append(f"时间步数错误: 期望100，实际{shape[1]}")
        
        # 检查特征维度是否为2048
        if shape[0] != 2048:
            result['valid'] = False
            result['errors'].append(f"特征维度错误: 期望2048，实际{shape[0]}")
        
        # 检查是否有NaN值
        if df.isnull().any().any():
            result['valid'] = False
            result['errors'].append("存在NaN值")
        
        # 检查是否有无穷大值
        if np.isinf(df.values).any():
            result['valid'] = False
            result['errors'].append("存在无穷大值")
        
        # 检查数值范围（警告）
        min_val = df.values.min()
        max_val = df.values.max()
        
        if abs(min_val) > 1000 or abs(max_val) > 1000:
            result['warnings'].append(f"特征值范围较大: [{min_val:.2f}, {max_val:.2f}]")
        
        # 检查header格式
        expected_headers = [f't{i}' for i in range(100)]
        if list(df.columns) != expected_headers:
            result['warnings'].append("CSV header格式不标准")
        
    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"读取文件失败: {e}")
    
    return result

def validate_annotation_file(ann_file, feature_dir):
    """
    验证标注文件与特征文件的一致性
    
    Args:
        ann_file (str): 标注文件路径
        feature_dir (str): 特征文件目录
    
    Returns:
        dict: 验证结果
    """
    result = {
        'file': ann_file,
        'valid': True,
        'errors': [],
        'warnings': [],
        'total_videos': 0,
        'missing_features': [],
        'extra_features': []
    }
    
    try:
        # 读取标注文件
        with open(ann_file, 'r', encoding='utf-8') as f:
            annotations = json.load(f)
        
        result['total_videos'] = len(annotations)
        
        # 获取特征文件列表
        feature_files = set()
        feature_path = Path(feature_dir)
        if feature_path.exists():
            feature_files = {f.stem for f in feature_path.rglob('*.csv')}
        
        # 检查每个标注视频是否有对应的特征文件
        annotated_videos = set(annotations.keys())
        
        # 找出缺失的特征文件
        missing = annotated_videos - feature_files
        if missing:
            result['missing_features'] = list(missing)
            result['errors'].append(f"缺失{len(missing)}个特征文件")
        
        # 找出多余的特征文件
        extra = feature_files - annotated_videos
        if extra:
            result['extra_features'] = list(extra)
            result['warnings'].append(f"存在{len(extra)}个多余的特征文件")
        
        # 验证标注格式
        for video_id, video_data in annotations.items():
            required_keys = ['duration_second', 'duration_frame', 'annotations', 'feature_frame', 'fps']
            for key in required_keys:
                if key not in video_data:
                    result['errors'].append(f"视频{video_id}缺少字段: {key}")
                    result['valid'] = False
            
            # 检查feature_frame是否为100
            if video_data.get('feature_frame') != 100:
                result['warnings'].append(f"视频{video_id}的feature_frame不是100")
        
    except Exception as e:
        result['valid'] = False
        result['errors'].append(f"读取标注文件失败: {e}")
    
    return result

def validate_all_features(feature_dir, ann_train, ann_val):
    """
    验证所有特征文件和标注文件
    
    Args:
        feature_dir (str): 特征文件目录
        ann_train (str): 训练集标注文件
        ann_val (str): 验证集标注文件
    """
    print("=" * 60)
    print("BMN特征格式验证报告")
    print("=" * 60)
    
    # 验证特征目录是否存在
    feature_path = Path(feature_dir)
    if not feature_path.exists():
        print(f"❌ 特征目录不存在: {feature_dir}")
        return
    
    # 递归获取所有CSV文件
    csv_files = list(feature_path.rglob('*.csv'))
    print(f"📁 特征目录: {feature_dir}")
    print(f"📄 找到 {len(csv_files)} 个CSV特征文件")
    
    if not csv_files:
        print("❌ 没有找到CSV特征文件")
        return
    
    # 验证特征文件
    print("\n🔍 验证特征文件格式...")
    valid_count = 0
    error_count = 0
    warning_count = 0
    
    for i, csv_file in enumerate(csv_files[:5]):  # 只验证前5个文件作为示例
        result = validate_csv_feature(str(csv_file))
        
        if result['valid']:
            valid_count += 1
            print(f"✅ {csv_file.name}: {result['shape']} (特征维度×时间步)")
        else:
            error_count += 1
            print(f"❌ {csv_file.name}: {', '.join(result['errors'])}")
        
        if result['warnings']:
            warning_count += len(result['warnings'])
            for warning in result['warnings']:
                print(f"⚠️  {csv_file.name}: {warning}")
    
    if len(csv_files) > 5:
        print(f"... (只显示前5个文件的详细结果)")
    
    # 验证标注文件
    print(f"\n🔍 验证标注文件...")
    
    for ann_file, name in [(ann_train, '训练集'), (ann_val, '验证集')]:
        if os.path.exists(ann_file):
            result = validate_annotation_file(ann_file, feature_dir)
            
            if result['valid']:
                print(f"✅ {name}标注文件: {result['total_videos']} 个视频")
            else:
                print(f"❌ {name}标注文件: {', '.join(result['errors'])}")
            
            if result['warnings']:
                for warning in result['warnings']:
                    print(f"⚠️  {name}: {warning}")
            
            if result['missing_features']:
                print(f"❌ {name}: 缺失特征文件: {result['missing_features'][:3]}...")
        else:
            print(f"❌ {name}标注文件不存在: {ann_file}")
    
    # 总结
    print(f"\n📊 验证总结:")
    print(f"   ✅ 有效特征文件: {valid_count}")
    print(f"   ❌ 错误: {error_count}")
    print(f"   ⚠️  警告: {warning_count}")
    
    if error_count == 0:
        print(f"\n🎉 所有特征文件格式正确，可以用于BMN训练!")
    else:
        print(f"\n⚠️  发现 {error_count} 个错误，请修复后再进行BMN训练")

def main():
    parser = argparse.ArgumentParser(description='验证BMN特征格式')
    parser.add_argument('--feature-dir', default='../data/MultiClassTAD/features_slowonly',
                       help='特征文件目录')
    parser.add_argument('--ann-train', default='../data/MultiClassTAD/multiclass_tad_train.json',
                       help='训练集标注文件')
    parser.add_argument('--ann-val', default='../data/MultiClassTAD/multiclass_tad_val.json',
                       help='验证集标注文件')
    
    args = parser.parse_args()
    
    validate_all_features(args.feature_dir, args.ann_train, args.ann_val)

if __name__ == '__main__':
    main()
