#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复BMN输入维度的变换
"""

import numpy as np
from mmcv.transforms import BaseTransform
from mmaction.registry import TRANSFORMS


@TRANSFORMS.register_module()
class LoadLocalizationFeatureForBMN(BaseTransform):
    """
    为BMN模型加载时间动作定位特征文件
    
    BMN期望的输入格式是 [batch_size, feat_dim, temporal_dim]
    但标准的LoadLocalizationFeature产生的是 [temporal_dim, feat_dim]
    
    这个变换直接加载CSV文件而不进行转置，以匹配BMN的期望格式。
    
    Args:
        raw_feature_ext (str): 特征文件扩展名。默认: '.csv'
    """
    
    def __init__(self, raw_feature_ext='.csv'):
        self.raw_feature_ext = raw_feature_ext
    
    def transform(self, results):
        """
        执行特征加载变换
        
        Args:
            results (dict): 包含数据信息的字典，需要包含'feature_path'键
            
        Returns:
            dict: 添加了'raw_feature'键的结果字典
        """
        data_path = results['feature_path']
        
        # 加载CSV文件
        raw_feature = np.loadtxt(
            data_path, dtype=np.float32, delimiter=',', skiprows=1)
        
        # 不进行转置，保持原始格式 (feat_dim, temporal_dim)
        # 这样批处理后会变成 [batch_size, feat_dim, temporal_dim]，符合BMN期望
        results['raw_feature'] = raw_feature
        
        return results
    
    def __repr__(self):
        repr_str = f'{self.__class__.__name__}(raw_feature_ext={self.raw_feature_ext})'
        return repr_str


@TRANSFORMS.register_module()
class TransposeFeature(BaseTransform):
    """
    转置特征矩阵的维度
    
    Args:
        axes (tuple): 转置的轴顺序，默认为 (1, 0) 即交换前两个维度
    """
    
    def __init__(self, axes=(1, 0)):
        self.axes = axes
    
    def transform(self, results):
        """
        执行特征转置
        
        Args:
            results (dict): 包含数据信息的字典，需要包含'raw_feature'键
            
        Returns:
            dict: 修改了'raw_feature'的结果字典
        """
        if 'raw_feature' not in results:
            raise KeyError("results中缺少'raw_feature'键")
        
        # 转置特征矩阵
        results['raw_feature'] = np.transpose(results['raw_feature'], self.axes)
        
        return results
    
    def __repr__(self):
        repr_str = f'{self.__class__.__name__}(axes={self.axes})'
        return repr_str
