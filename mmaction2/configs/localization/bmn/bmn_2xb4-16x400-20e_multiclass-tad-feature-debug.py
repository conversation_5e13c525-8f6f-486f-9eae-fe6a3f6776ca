# 多类别TAD配置文件 - 调试版本
# 支持三个类别：ok, nok_electric_defect, nok_appearance_defect
# 降低检测阈值以便更容易检测到动作

# model settings
model = dict(
    type='BMN',
    temporal_dim=16,  # 每个片段16个时间步
    boundary_ratio=0.5,
    num_samples=32,
    num_samples_per_bin=3,
    feat_dim=400,  # 特征维度
    soft_nms_alpha=0.4,
    soft_nms_low_threshold=0.1,   # 降低阈值：从0.5降到0.1
    soft_nms_high_threshold=0.6,  # 降低阈值：从0.9降到0.6
    post_process_top_k=200)       # 增加输出数量：从100增到200

# dataset settings
dataset_type = 'ActivityNetDataset'
data_root = 'data/MultiClassTAD/features/'
data_root_val = 'data/MultiClassTAD/features/'
ann_file_train = 'data/MultiClassTAD/multiclass_tad_train.json'
ann_file_val = 'data/MultiClassTAD/multiclass_tad_val.json'
ann_file_test = 'data/MultiClassTAD/multiclass_tad_val.json'

train_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='ResizeLocalizationFeature', temporal_dim=16, interpolation_mode='linear'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', ))
]

val_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='ResizeLocalizationFeature', temporal_dim=16, interpolation_mode='linear'),
    dict(type='GenerateLocalizationLabels'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

test_pipeline = [
    dict(type='LoadLocalizationFeature'),
    dict(type='ResizeLocalizationFeature', temporal_dim=16, interpolation_mode='linear'),
    dict(
        type='PackLocalizationInputs',
        keys=('gt_bbox', ),
        meta_keys=('video_name', 'duration_second', 'duration_frame',
                   'annotations', 'feature_frame'))
]

train_dataloader = dict(
    batch_size=4,  # 适中的batch size
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=True),
    drop_last=True,
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_train,
        data_prefix=dict(video=data_root),
        pipeline=train_pipeline))

val_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_val,
        data_prefix=dict(video=data_root_val),
        pipeline=val_pipeline,
        test_mode=True))

test_dataloader = dict(
    batch_size=1,
    num_workers=4,
    persistent_workers=True,
    sampler=dict(type='DefaultSampler', shuffle=False),
    dataset=dict(
        type=dataset_type,
        ann_file=ann_file_test,
        data_prefix=dict(video=data_root_val),
        pipeline=test_pipeline,
        test_mode=True))

# 训练设置
max_epochs = 30  # 增加训练轮数，因为是多类别任务
train_cfg = dict(
    type='EpochBasedTrainLoop',
    max_epochs=max_epochs,
    val_begin=1,
    val_interval=3)  # 每3个epoch验证一次

val_cfg = dict(type='ValLoop')
test_cfg = dict(type='TestLoop')

# 优化器设置 - 针对多类别调整
optim_wrapper = dict(
    optimizer=dict(type='Adam', lr=0.0005, weight_decay=0.0001),  # 稍微降低学习率
    clip_grad=dict(max_norm=40, norm_type=2))

# 学习率调度
param_scheduler = [
    dict(
        type='MultiStepLR',
        begin=0,
        end=max_epochs,
        by_epoch=True,
        milestones=[20, 25],  # 在第20和25个epoch降低学习率
        gamma=0.1)
]

# 工作目录
work_dir = './work_dirs/bmn_multiclass_tad/'

# 评估设置
test_evaluator = dict(
    type='ANetMetric',
    metric_type='AR@AN',
    dump_config=dict(out=f'{work_dir}/results.json', output_format='json'))
val_evaluator = test_evaluator

# 日志和检查点设置
default_hooks = dict(
    timer=dict(type='IterTimerHook'),
    logger=dict(type='LoggerHook', interval=10, ignore_last=False),
    param_scheduler=dict(type='ParamSchedulerHook'),
    checkpoint=dict(type='CheckpointHook', interval=5, save_best='auto'),
    sampler_seed=dict(type='DistSamplerSeedHook'),
    sync_buffers=dict(type='SyncBuffersHook'))

# 可视化设置
vis_backends = [dict(type='LocalVisBackend')]
visualizer = dict(
    type='ActionVisualizer', vis_backends=vis_backends, name='visualizer')

# 运行时设置
default_scope = 'mmaction'
env_cfg = dict(
    cudnn_benchmark=False,
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    dist_cfg=dict(backend='nccl'))

log_processor = dict(type='LogProcessor', window_size=20, by_epoch=True)
log_level = 'INFO'
load_from = None
resume = False

# 类别信息
num_classes = 3
class_names = ['nok_appearance_defect', 'nok_electric_defect', 'ok']
