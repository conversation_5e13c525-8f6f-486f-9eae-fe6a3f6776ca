#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试特征提取流水线的各个组件
"""

import os
import sys
import json
import numpy as np
import pandas as pd
from pathlib import Path

# 添加data_process到路径
sys.path.append('../data_process')

def test_video_list_generation():
    """测试视频列表生成"""
    print("🧪 测试视频列表生成...")
    
    try:
        from generate_video_list import generate_video_list
        
        # 切换到data_process目录执行
        original_cwd = os.getcwd()
        os.chdir('../data_process')
        
        video_list_file = generate_video_list()
        
        # 恢复工作目录
        os.chdir(original_cwd)
        
        # 验证文件是否存在
        if os.path.exists(video_list_file):
            with open(video_list_file, 'r') as f:
                lines = f.readlines()
            print(f"✅ 视频列表生成成功: {len(lines)} 个视频")
            return True
        else:
            print(f"❌ 视频列表文件不存在: {video_list_file}")
            return False
            
    except Exception as e:
        print(f"❌ 视频列表生成失败: {e}")
        return False

def test_annotation_generation():
    """测试标注文件生成"""
    print("🧪 测试标注文件生成...")
    
    try:
        from generate_activitynet_annotations import generate_annotations
        
        # 切换到data_process目录执行
        original_cwd = os.getcwd()
        os.chdir('../data_process')
        
        train_file, val_file = generate_annotations()
        
        # 恢复工作目录
        os.chdir(original_cwd)
        
        # 验证文件是否存在和格式是否正确
        success = True
        for ann_file, name in [(train_file, '训练集'), (val_file, '验证集')]:
            if os.path.exists(ann_file):
                with open(ann_file, 'r') as f:
                    data = json.load(f)
                print(f"✅ {name}标注文件生成成功: {len(data)} 个视频")
            else:
                print(f"❌ {name}标注文件不存在: {ann_file}")
                success = False
        
        return success
        
    except Exception as e:
        print(f"❌ 标注文件生成失败: {e}")
        return False

def test_feature_postprocessing():
    """测试特征后处理功能"""
    print("🧪 测试特征后处理功能...")
    
    try:
        from postprocess_features import pool_feature_to_100_steps
        
        # 创建测试特征数据
        # 模拟SlowOnly输出：不同时间长度，2048维特征
        test_cases = [
            np.random.randn(50, 2048),   # 50个时间步
            np.random.randn(150, 2048),  # 150个时间步
            np.random.randn(1, 2048),    # 1个时间步
        ]
        
        for i, test_feature in enumerate(test_cases):
            pooled = pool_feature_to_100_steps(test_feature)
            
            if pooled.shape == (100, 2048):
                print(f"✅ 测试用例{i+1}: {test_feature.shape} -> {pooled.shape}")
            else:
                print(f"❌ 测试用例{i+1}: 期望(100, 2048)，实际{pooled.shape}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 特征后处理测试失败: {e}")
        return False

def test_csv_format():
    """测试CSV格式生成"""
    print("🧪 测试CSV格式生成...")
    
    try:
        # 创建测试特征并保存为CSV
        test_feature = np.random.randn(100, 2048)  # 100时间步，2048维
        test_feature_transposed = test_feature.T   # 转置为2048x100
        
        # 创建临时CSV文件
        test_csv = Path('../data/MultiClassTAD/test_feature.csv')
        test_csv.parent.mkdir(parents=True, exist_ok=True)
        
        # 生成CSV内容
        lines = []
        header = ','.join([f't{i}' for i in range(100)])
        lines.append(header)
        
        for i in range(2048):
            line = ','.join([f'{x:.6f}' for x in test_feature_transposed[i]])
            lines.append(line)
        
        # 写入文件
        with open(test_csv, 'w') as f:
            f.write('\n'.join(lines))
        
        # 验证读取
        df = pd.read_csv(test_csv)
        
        if df.shape == (2048, 100):
            print(f"✅ CSV格式测试成功: {df.shape}")
            
            # 清理测试文件
            test_csv.unlink()
            return True
        else:
            print(f"❌ CSV格式错误: 期望(2048, 100)，实际{df.shape}")
            return False
            
    except Exception as e:
        print(f"❌ CSV格式测试失败: {e}")
        return False

def test_bmn_config():
    """测试BMN配置文件"""
    print("🧪 测试BMN配置文件...")
    
    config_file = '../data_process/bmn_multiclass_tad_config.py'
    
    if not os.path.exists(config_file):
        print(f"❌ BMN配置文件不存在: {config_file}")
        return False
    
    try:
        # 尝试导入配置（简单语法检查）
        import importlib.util
        spec = importlib.util.spec_from_file_location("config", config_file)
        config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config)
        
        # 检查关键配置项
        if hasattr(config, 'model') and config.model.get('feat_dim') == 2048:
            print("✅ BMN配置文件格式正确，特征维度设置为2048")
            return True
        else:
            print("❌ BMN配置文件中特征维度设置错误")
            return False
            
    except Exception as e:
        print(f"❌ BMN配置文件测试失败: {e}")
        return False

def main():
    """运行所有测试"""
    print("🧪 BMN特征提取流水线测试")
    print("=" * 60)
    
    tests = [
        ("视频列表生成", test_video_list_generation),
        ("标注文件生成", test_annotation_generation),
        ("特征后处理", test_feature_postprocessing),
        ("CSV格式", test_csv_format),
        ("BMN配置", test_bmn_config),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            print()
    
    print("=" * 60)
    print(f"🧪 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！流水线组件工作正常")
    else:
        print("⚠️  部分测试失败，请检查相关组件")

if __name__ == '__main__':
    main()
